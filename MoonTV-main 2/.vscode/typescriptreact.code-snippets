{
  //#region  //*=========== React ===========
  "import React": {
    "prefix": "ir",
    "body": ["import * as React from 'react';"]
  },
  "React.useState": {
    "prefix": "us",
    "body": [
      "const [${1}, set${1/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}] = React.useState<$3>(${2:initial${1/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}})$0"
    ]
  },
  "React.useEffect": {
    "prefix": "uf",
    "body": ["React.useEffect(() => {", "  $0", "}, []);"]
  },
  "React.useReducer": {
    "prefix": "ur",
    "body": [
      "const [state, dispatch] = React.useReducer(${0:someReducer}, {",
      "  ",
      "})"
    ]
  },
  "React.useRef": {
    "prefix": "urf",
    "body": ["const ${1:someRef} = React.useRef($0)"]
  },
  "React Functional Component": {
    "prefix": "rc",
    "body": [
      "import * as React from 'react';\n",
      "export default function ${1:${TM_FILENAME_BASE}}() {",
      "  return (",
      "    <div>",
      "      $0",
      "    </div>",
      "  )",
      "}"
    ]
  },
  "React Functional Component with Props": {
    "prefix": "rcp",
    "body": [
      "import * as React from 'react';\n",
      "import clsxm from '@/lib/clsxm';\n",
      "type ${1:${TM_FILENAME_BASE}}Props= {\n",
      "} & React.ComponentPropsWithoutRef<'div'>\n",
      "export default function ${1:${TM_FILENAME_BASE}}({className, ...rest}: ${1:${TM_FILENAME_BASE}}Props) {",
      "  return (",
      "    <div className={clsxm(['', className])} {...rest}>",
      "      $0",
      "    </div>",
      "  )",
      "}"
    ]
  },
  //#endregion  //*======== React ===========

  //#region  //*=========== Commons ===========
  "Region": {
    "prefix": "reg",
    "scope": "javascript, typescript, javascriptreact, typescriptreact",
    "body": [
      "//#region  //*=========== ${1} ===========",
      "${TM_SELECTED_TEXT}$0",
      "//#endregion  //*======== ${1} ==========="
    ]
  },
  "Region CSS": {
    "prefix": "regc",
    "scope": "css, scss",
    "body": [
      "/* #region  /**=========== ${1} =========== */",
      "${TM_SELECTED_TEXT}$0",
      "/* #endregion  /**======== ${1} =========== */"
    ]
  },
  //#endregion  //*======== Commons ===========

  //#region  //*=========== Next.js ===========
  "Next Pages": {
    "prefix": "np",
    "body": [
      "import * as React from 'react';\n",
      "import Layout from '@/components/layout/Layout';",
      "import Seo from '@/components/Seo';\n",
      "export default function ${1:${TM_FILENAME_BASE/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}}Page() {",
      "  return (",
      "    <Layout>",
      "      <Seo templateTitle='${1:${TM_FILENAME_BASE/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}}' />\n",
      "      <main>\n",
      "        <section className=''>",
      "          <div className='layout py-20 min-h-screen'>",
      "            $0",
      "          </div>",
      "        </section>",
      "      </main>",
      "    </Layout>",
      "  )",
      "}"
    ]
  },
  "Next API": {
    "prefix": "napi",
    "body": [
      "import { NextApiRequest, NextApiResponse } from 'next';\n",
      "export default async function handler(req: NextApiRequest, res: NextApiResponse) {",
      "  if (req.method === 'GET') {",
      "    res.status(200).json({ name: 'Bambang' });",
      "  } else {",
      "    res.status(405).json({ message: 'Method Not Allowed' });",
      "  }",
      "}"
    ]
  },
  "Get Static Props": {
    "prefix": "gsp",
    "body": [
      "export const getStaticProps = async (context: GetStaticPropsContext) => {",
      "  return {",
      "    props: {}",
      "  };",
      "}"
    ]
  },
  "Get Static Paths": {
    "prefix": "gspa",
    "body": [
      "export const getStaticPaths: GetStaticPaths = async () => {",
      "  return {",
      "    paths: [",
      "      { params: { $1 }}",
      "    ],",
      "    fallback: ",
      "  };",
      "}"
    ]
  },
  "Get Server Side Props": {
    "prefix": "gssp",
    "body": [
      "export const getServerSideProps = async (context: GetServerSidePropsContext) => {",
      "  return {",
      "    props: {}",
      "  };",
      "}"
    ]
  },
  "Infer Get Static Props": {
    "prefix": "igsp",
    "body": "InferGetStaticPropsType<typeof getStaticProps>"
  },
  "Infer Get Server Side Props": {
    "prefix": "igssp",
    "body": "InferGetServerSidePropsType<typeof getServerSideProps>"
  },
  "Import useRouter": {
    "prefix": "imust",
    "body": ["import { useRouter } from 'next/router';"]
  },
  "Import Next Image": {
    "prefix": "imimg",
    "body": ["import Image from 'next/image';"]
  },
  "Import Next Link": {
    "prefix": "iml",
    "body": ["import Link from 'next/link';"]
  },
  //#endregion  //*======== Next.js ===========

  //#region  //*=========== Snippet Wrap ===========
  "Wrap with Fragment": {
    "prefix": "ff",
    "body": ["<>", "\t${TM_SELECTED_TEXT}", "</>"]
  },
  "Wrap with clsx": {
    "prefix": "cx",
    "body": ["{clsx([${TM_SELECTED_TEXT}$0])}"]
  },
  "Wrap with clsxm": {
    "prefix": "cxm",
    "body": ["{clsxm([${TM_SELECTED_TEXT}$0, className])}"]
  },
  //#endregion  //*======== Snippet Wrap ===========

  "Logger": {
    "prefix": "lg",
    "body": [
      "logger({ ${1:${CLIPBOARD}} }, '${TM_FILENAME} line ${TM_LINE_NUMBER}')"
    ]
  }
}
