'use client';

import type { ThemeProviderProps } from 'next-themes';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import * as React from 'react';

export function ThemeProvider({ children }: ThemeProviderProps) {
  // 强制深色主题，关闭系统跟随与切换
  return (
    <NextThemesProvider
      attribute='class'
      defaultTheme='dark'
      enableSystem={false}
      forcedTheme='dark'
    >
      {children}
    </NextThemesProvider>
  );
}
