'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function HomePage() {
  const { user, loading, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  const handleLogout = async () => {
    await logout();
    router.push('/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
        <div className="text-white text-xl">加载中...</div>
      </div>
    );
  }

  if (!user) {
    return null; // 会被重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* 导航栏 */}
      <nav className="bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">
                SP破解站
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-white">
                <span className="text-sm text-gray-300">欢迎，</span>
                <span className="font-medium">{user.username}</span>
                {user.vip.isVip && (
                  <span className="ml-2 px-2 py-1 bg-yellow-500 text-black text-xs rounded-full">
                    VIP
                  </span>
                )}
              </div>
              
              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              欢迎来到SP破解站
            </h2>
            <p className="text-gray-300 text-lg">
              专为VIP会员提供的高质量破解资源
            </p>
          </div>

          {/* VIP信息卡片 */}
          <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl p-6 mb-8 border border-yellow-500/30">
            <h3 className="text-xl font-semibold text-white mb-4">
              您的VIP信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-white">
              <div>
                <span className="text-gray-300">VIP等级：</span>
                <span className="font-medium">Level {user.vip.level}</span>
              </div>
              <div>
                <span className="text-gray-300">状态：</span>
                <span className="font-medium text-green-400">
                  {user.vip.status === 'active' ? '有效' : '未知'}
                </span>
              </div>
              {user.vip.expire && (
                <div className="md:col-span-2">
                  <span className="text-gray-300">到期时间：</span>
                  <span className="font-medium">
                    {new Date(user.vip.expire).toLocaleDateString('zh-CN')}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 功能区域 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
              <div className="text-blue-400 text-3xl mb-4">🎮</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                游戏破解
              </h3>
              <p className="text-gray-300">
                最新的游戏破解资源和补丁
              </p>
            </div>

            <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
              <div className="text-green-400 text-3xl mb-4">💻</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                软件破解
              </h3>
              <p className="text-gray-300">
                专业软件的破解版本和激活工具
              </p>
            </div>

            <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
              <div className="text-purple-400 text-3xl mb-4">📱</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                移动应用
              </h3>
              <p className="text-gray-300">
                手机应用的破解和修改版本
              </p>
            </div>

            <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
              <div className="text-yellow-400 text-3xl mb-4">🎬</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                影音资源
              </h3>
              <p className="text-gray-300">
                高清电影、电视剧和音乐资源
              </p>
            </div>

            <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
              <div className="text-red-400 text-3xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                学习资料
              </h3>
              <p className="text-gray-300">
                编程教程、技术文档和学习资源
              </p>
            </div>

            <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer">
              <div className="text-indigo-400 text-3xl mb-4">⚙️</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                系统工具
              </h3>
              <p className="text-gray-300">
                系统优化、安全防护和实用工具
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
