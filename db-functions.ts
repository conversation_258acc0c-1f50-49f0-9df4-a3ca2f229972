// WordPress密码验证函数
import crypto from 'crypto';

function wpHashPassword(password: string, setting: string): string {
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  
  if (setting.length < 12) {
    return '*';
  }
  
  const count_log2 = itoa64.indexOf(setting[3]);
  if (count_log2 < 7 || count_log2 > 30) {
    return '*';
  }
  
  const count = 1 << count_log2;
  const salt = setting.slice(4, 12);
  
  if (salt.length !== 8) {
    return '*';
  }
  
  let hash = crypto.createHash('md5').update(salt + password).digest();
  
  for (let i = 0; i < count; i++) {
    hash = crypto.createHash('md5').update(Buffer.concat([hash, Buffer.from(password)])).digest();
  }
  
  let output = setting.slice(0, 12);
  let i = 0;
  
  while (i < 16) {
    let value = hash[i++];
    output += itoa64[value & 0x3f];
    
    if (i < 16) {
      value |= hash[i] << 8;
    }
    output += itoa64[(value >> 6) & 0x3f];
    
    if (i++ >= 16) {
      break;
    }
    
    if (i < 16) {
      value |= hash[i] << 16;
    }
    output += itoa64[(value >> 12) & 0x3f];
    
    if (i++ >= 16) {
      break;
    }
    
    output += itoa64[(value >> 18) & 0x3f];
  }
  
  return output;
}

function wpCheckPassword(password: string, hash: string): boolean {
  if (hash.startsWith('$P$') || hash.startsWith('$H$')) {
    return wpHashPassword(password, hash) === hash;
  }
  
  // 简单MD5检查（旧版WordPress）
  return crypto.createHash('md5').update(password).digest('hex') === hash;
}

// 验证WordPress用户
export async function verifyWordPressUser(username: string, password: string) {
  try {
    const connection = await pool.getConnection();
    
    // 查询用户信息
    const [userRows] = await connection.execute(
      `SELECT 
        u.ID as id,
        u.user_login as username,
        u.user_email as email,
        u.user_pass as password_hash,
        u.display_name as displayName,
        COALESCE(m1.meta_value, '') as vip_level,
        COALESCE(m2.meta_value, '') as vip_expire,
        COALESCE(m3.meta_value, '') as vip_status,
        COALESCE(m4.meta_value, '') as user_level
      FROM wp_users u
      LEFT JOIN wp_usermeta m1 ON u.ID = m1.user_id AND m1.meta_key = 'vip_level'
      LEFT JOIN wp_usermeta m2 ON u.ID = m2.user_id AND m2.meta_key = 'vip_expire'
      LEFT JOIN wp_usermeta m3 ON u.ID = m3.user_id AND m3.meta_key = 'vip_status'
      LEFT JOIN wp_usermeta m4 ON u.ID = m4.user_id AND m4.meta_key = 'wp_user_level'
      WHERE u.user_login = ? OR u.user_email = ?`,
      [username, username]
    ) as any;
    
    connection.release();
    
    if (userRows.length === 0) {
      return null;
    }
    
    const user = userRows[0];
    
    // 验证密码
    if (!wpCheckPassword(password, user.password_hash)) {
      return null;
    }
    
    // 检查VIP状态
    const vipLevel = parseInt(user.vip_level) || 0;
    const vipExpire = user.vip_expire ? new Date(user.vip_expire) : null;
    const vipStatus = user.vip_status;
    const userLevel = parseInt(user.user_level) || 0;
    
    const now = new Date();
    const isVip = vipLevel > 0 || 
                  vipStatus === 'active' || 
                  userLevel >= 5 ||
                  (vipExpire && vipExpire > now);
    
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      displayName: user.displayName,
      vip: {
        isVip,
        level: vipLevel,
        expire: vipExpire,
        status: vipStatus
      }
    };
    
  } catch (error) {
    console.error('Database error:', error);
    throw error;
  }
}
