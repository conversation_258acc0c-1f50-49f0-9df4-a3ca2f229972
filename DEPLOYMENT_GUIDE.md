# SP破解站登录系统部署指南

## 🎯 部署概述

本指南将帮助您完成SP破解站的登录系统部署，包括：
- ✅ 用户认证API
- ✅ 登录页面
- ✅ 认证上下文管理
- ✅ WordPress密码验证
- ✅ VIP权限控制

## 📋 部署步骤

### 1. 安装依赖

```bash
cd /www/wwwroot/sp.nspojie.com
npm install jsonwebtoken @types/jsonwebtoken
```

### 2. 更新数据库函数

在 `src/lib/db.ts` 文件末尾添加以下代码：

```typescript
// WordPress密码验证函数
import crypto from 'crypto';

function wpHashPassword(password: string, setting: string): string {
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  
  if (setting.length < 12) {
    return '*';
  }
  
  const count_log2 = itoa64.indexOf(setting[3]);
  if (count_log2 < 7 || count_log2 > 30) {
    return '*';
  }
  
  const count = 1 << count_log2;
  const salt = setting.slice(4, 12);
  
  if (salt.length !== 8) {
    return '*';
  }
  
  let hash = crypto.createHash('md5').update(salt + password).digest();
  
  for (let i = 0; i < count; i++) {
    hash = crypto.createHash('md5').update(Buffer.concat([hash, Buffer.from(password)])).digest();
  }
  
  let output = setting.slice(0, 12);
  let i = 0;
  
  while (i < 16) {
    let value = hash[i++];
    output += itoa64[value & 0x3f];
    
    if (i < 16) {
      value |= hash[i] << 8;
    }
    output += itoa64[(value >> 6) & 0x3f];
    
    if (i++ >= 16) {
      break;
    }
    
    if (i < 16) {
      value |= hash[i] << 16;
    }
    output += itoa64[(value >> 12) & 0x3f];
    
    if (i++ >= 16) {
      break;
    }
    
    output += itoa64[(value >> 18) & 0x3f];
  }
  
  return output;
}

function wpCheckPassword(password: string, hash: string): boolean {
  if (hash.startsWith('$P$') || hash.startsWith('$H$')) {
    return wpHashPassword(password, hash) === hash;
  }
  
  // 简单MD5检查（旧版WordPress）
  return crypto.createHash('md5').update(password).digest('hex') === hash;
}

// 验证WordPress用户
export async function verifyWordPressUser(username: string, password: string) {
  try {
    const connection = await pool.getConnection();
    
    // 查询用户信息
    const [userRows] = await connection.execute(
      `SELECT 
        u.ID as id,
        u.user_login as username,
        u.user_email as email,
        u.user_pass as password_hash,
        u.display_name as displayName,
        COALESCE(m1.meta_value, '') as vip_level,
        COALESCE(m2.meta_value, '') as vip_expire,
        COALESCE(m3.meta_value, '') as vip_status,
        COALESCE(m4.meta_value, '') as user_level
      FROM wp_users u
      LEFT JOIN wp_usermeta m1 ON u.ID = m1.user_id AND m1.meta_key = 'vip_level'
      LEFT JOIN wp_usermeta m2 ON u.ID = m2.user_id AND m2.meta_key = 'vip_expire'
      LEFT JOIN wp_usermeta m3 ON u.ID = m3.user_id AND m3.meta_key = 'vip_status'
      LEFT JOIN wp_usermeta m4 ON u.ID = m4.user_id AND m4.meta_key = 'wp_user_level'
      WHERE u.user_login = ? OR u.user_email = ?`,
      [username, username]
    ) as any;
    
    connection.release();
    
    if (userRows.length === 0) {
      return null;
    }
    
    const user = userRows[0];
    
    // 验证密码
    if (!wpCheckPassword(password, user.password_hash)) {
      return null;
    }
    
    // 检查VIP状态
    const vipLevel = parseInt(user.vip_level) || 0;
    const vipExpire = user.vip_expire ? new Date(user.vip_expire) : null;
    const vipStatus = user.vip_status;
    const userLevel = parseInt(user.user_level) || 0;
    
    const now = new Date();
    const isVip = vipLevel > 0 || 
                  vipStatus === 'active' || 
                  userLevel >= 5 ||
                  (vipExpire && vipExpire > now);
    
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      displayName: user.displayName,
      vip: {
        isVip,
        level: vipLevel,
        expire: vipExpire,
        status: vipStatus
      }
    };
    
  } catch (error) {
    console.error('Database error:', error);
    throw error;
  }
}
```

### 3. 创建API路由

#### 3.1 创建登录API
创建文件 `src/app/api/auth/login/route.ts`：

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { verifyWordPressUser } from '@/lib/db';
import jwt from 'jsonwebtoken';

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    if (!username || !password) {
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: 400 }
      );
    }

    // 验证WordPress用户
    const user = await verifyWordPressUser(username, password);
    
    if (!user) {
      return NextResponse.json(
        { error: '用户名或密码错误' },
        { status: 401 }
      );
    }

    // 检查VIP状态
    if (!user.vip.isVip) {
      return NextResponse.json(
        { error: '您不是VIP会员，无法访问本站' },
        { status: 403 }
      );
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        email: user.email,
        vip: user.vip
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    // 设置cookie
    const response = NextResponse.json({
      success: true,
      message: '登录成功',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        displayName: user.displayName,
        vip: user.vip
      }
    });

    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 // 7 days
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
```

#### 3.2 创建登出API
创建文件 `src/app/api/auth/logout/route.ts`：

```typescript
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const response = NextResponse.json({
      success: true,
      message: '登出成功'
    });

    // 清除cookie
    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0
    });

    return response;

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
```

#### 3.3 创建用户验证API
创建文件 `src/app/api/auth/me/route.ts`：

```typescript
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      );
    }

    // 验证JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;

    return NextResponse.json({
      success: true,
      user: {
        id: decoded.userId,
        username: decoded.username,
        email: decoded.email,
        vip: decoded.vip
      }
    });

  } catch (error) {
    console.error('Auth verification error:', error);
    return NextResponse.json(
      { error: '登录已过期，请重新登录' },
      { status: 401 }
    );
  }
}
```

### 4. 创建认证上下文

创建文件 `src/contexts/AuthContext.tsx`：

[内容见 AuthContext.tsx 文件]

### 5. 创建登录页面

创建文件 `src/app/login/page.tsx`：

[内容见 login-page.tsx 文件]

### 6. 更新主页

更新文件 `src/app/page.tsx`：

[内容见 homepage.tsx 文件]

### 7. 更新根布局

更新文件 `src/app/layout.tsx`：

```typescript
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'SP破解站 - VIP专享',
  description: '专为VIP会员提供的高质量破解资源',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

### 8. 构建和重启

```bash
# 构建项目
npm run build

# 重启PM2服务
export PATH="/www/server/nodejs/v24.4.0/bin:$PATH"
pm2 restart sp.nspojie.com

# 检查状态
pm2 status
```

## 🎉 部署完成

访问地址：
- **主站**: https://sp.nspojie.com
- **登录页**: https://sp.nspojie.com/login

## 🔧 功能特性

✅ **完整的用户认证系统**
- WordPress用户数据库集成
- 安全的密码验证
- JWT token认证
- 自动登录状态管理

✅ **VIP权限控制**
- 多种VIP状态检查
- 自动权限验证
- 非VIP用户拒绝访问

✅ **现代化UI设计**
- 响应式设计
- 渐变背景
- 毛玻璃效果
- 流畅动画

✅ **安全特性**
- HttpOnly cookies
- CSRF保护
- 密码加密验证
- 会话管理

## 🚀 下一步开发

现在基础登录系统已完成，可以继续开发：
1. 视频播放功能
2. 用户中心
3. 观看历史
4. 内容管理系统
